# TODO: Implement Pydantic Settings
# Xem hướng dẫn trong FASTAPI_GUIDE.md
from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings): 

    #database 
    DB_USER : str
    DB_PASSWORD: str
    DB_HOST: str
    DB_PORT: int 
    DB_NAME: str 
    DATABASE_URL: str 

    #security 
    SECRET_KEY: str 
    ALGORITHM: str 
    ACCESS_TOKEN_EXPIRE_MINUTES: int 

    #App Settings 
    APP_NAME: str 
    DEBUG: bool 
    VERSION: str 

    #CORS 
    ALLOWED_ORIGINS: List[str]

    class Config:
        # Tìm file .env trong cùng thư mục với file config.py
        env_file = os.path.join(os.path.dirname(__file__), "..", ".env")
        case_sensitive = True
        env_file_encoding = 'utf-8'
        
settings = Settings()
