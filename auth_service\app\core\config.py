# TODO: Implement Pydantic Settings
# Xem hướng dẫn trong FASTAPI_GUIDE.md
from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings): 

    #database 
    DB_USER : str
    DB_PASSWORD: str
    DB_HOST: str
    DB_PORT: int 
    DB_NAME: str 
    DATABASE_URL: str 

    #security 
    SECRET_KEY: str 
    ALGORITHM: str 
    ACCESS_TOKEN_EXPIRE_MINUTES: int 

    #App Settings 
    APP_NAME: str 
    DEBUG: bool 
    VERSION: str 

    #CORS 
    ALLOWED_ORIGINS: List[str]

    class Config: 
        env_file = ".env"
        case_sensitive = True

settings = Settings()
