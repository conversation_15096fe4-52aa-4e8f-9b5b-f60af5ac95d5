#!/usr/bin/env python3
"""
Test script để kiểm tra config.py có hoạt động không
"""

import sys
import os

# Thêm đường dẫn để import được module
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from core.config import settings
    print("✅ SUCCESS: Config loaded successfully!")
    print(f"📊 DB_HOST: {settings.DB_HOST}")
    print(f"🏷️  APP_NAME: {settings.APP_NAME}")
    print(f"🔐 SECRET_KEY: {settings.SECRET_KEY[:20]}...")
    print(f"🌐 DATABASE_URL: {settings.DATABASE_URL}")
    print(f"🚀 DEBUG: {settings.DEBUG}")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    print(f"Error type: {type(e).__name__}")
    import traceback
    traceback.print_exc()
